/* Alpha-Tick Pro RB 螺纹钢高频策略 - 文华财经版070402 */
//******************************************************************
//                 策略参数配置
//******************************************************************
INPUT:
    // 大单参数
    BigSingleRatio(3.2, 2.5, 4.0, 0.1),    // 大单标准差倍数(螺纹钢优化)
    OrderFlowPeriod(15, 10, 30, 1),         // 订单流分析周期
    
    // R-Breaker参数
    K1(0.28, 0.1, 0.4, 0.01),              // 基础突破系数(螺纹钢优化)
    K2(0.12, 0.05, 0.2, 0.005),            // 波动率调整系数
    ATRPeriod(14, 5, 30, 1),               // ATR计算周期
    
    // 风险管理
    MaxRiskRatio(0.02, 0.005, 0.05, 0.001), // 单笔最大风险
    DailyLossLimit(-0.03),                  // 单日止损线(-3%)
    MinLiquidity(80, 50, 150, 10);          // 最低流动性阈值(手)

//******************************************************************
//                 全局变量声明
//******************************************************************
VARS:
    // 核心状态变量
    BigVol(0), First(0), TradingPaused(0),
    DailyHigh(0), DailyLow(0), PreClose(0),
    ObserveSellPrice(0), ObserveBuyPrice(0),
    
    // 订单流分析
    CumulativeDelta(0), EMADelta(0),
    BuyMarketVol(0), SellMarketVol(0),
    
    // 交易管理
    PositionSize(0), VolatilityRatio(0), DynamicK(0),
    LastTradeTime(0), CurrentTime(0),
    BKID(0), SKID(0), BPID(0), SPID(0),
    LossStreak(0), DailyMaxProfit(0);

//******************************************************************
//                 初始化模块
//******************************************************************
INIT:
IF DATE <> REF(DATE, 1) THEN BEGIN
    PreClose = REF(CLOSE, 1);
    DailyHigh = HIGH;
    DailyLow = LOW;
    CumulativeDelta = 0;
    First = 0;
    DailyMaxProfit = 0;
    LossStreak = 0;
END;

// 螺纹钢合约乘数
ContractMultiplier = 10;

//******************************************************************
//                 核心计算模块
//******************************************************************

// 1. 动态大单阈值计算 (优化版)
CALCBigSingle:
IF BarStatus = 2 THEN BEGIN
    // 动态调整分析窗口(最近2小时)
    VAR: LookbackPeriod = IIF(TIME < 0.1130, 30, 120); // 早盘用30分钟，其他时段用2小时
    
    VAR: MeanVol(0), StdDev(0), i(0), Count(0);
    MeanVol = 0; StdDev = 0; Count = 0;
    
    FOR i = 0 TO MIN(DATACOUNT-1, LookbackPeriod) DO BEGIN
        MeanVol := MeanVol + VOLUME[i];
        Count := Count + 1;
    END;
    
    IF Count > 5 THEN BEGIN // 至少5根K线
        MeanVol := MeanVol / Count;
        
        FOR i = 0 TO MIN(DATACOUNT-1, LookbackPeriod) DO BEGIN
            StdDev := StdDev + SQUARE(VOLUME[i] - MeanVol);
        END;
        StdDev := SQRT(StdDev / Count);
        
        // 动态调整大单系数(根据市场波动率)
        VAR: DynamicRatio = BigSingleRatio * (1 + 0.5*VolatilityRatio);
        BigVol := MeanVol + DynamicRatio * StdDev;
        
        DEBUG('【动态大单】最新阈值:', BigVol,' 动态系数:', DynamicRatio);
    END;
END;
// 2. 增强版订单流分析
OrderFlowAnalysis:
// 使用三档数据加权计算
VAR: 
    WeightedDelta(0),
    DepthFactor(0.7); // 深度权重因子
    
WeightedDelta = (BIDVOL1 - ASKVOL1) * 0.5 
              + (BIDVOL2 - ASKVOL2) * 0.3 * DepthFactor
              + (BIDVOL3 - ASKVOL3) * 0.2 * DepthFactor;

// 动态平滑周期(市场活跃时缩短周期)
VAR: DynamicPeriod = IIF((BIDVOL1+ASKVOL1) > 2*MinLiquidity, 
                        MAX(5, OrderFlowPeriod-5), 
                        OrderFlowPeriod);

CumulativeDelta = CumulativeDelta + WeightedDelta;
EMADelta = EMA(WeightedDelta, DynamicPeriod);

// 增加大单净流向指标
IF VOLUME > BigVol THEN BEGIN
    IF CLOSE > (OPEN+HIGH+LOW)/3 THEN 
        BuyMarketVol := BuyMarketVol + VOLUME
    ELSE IF CLOSE < (OPEN+HIGH+LOW)/3 THEN
        SellMarketVol := SellMarketVol + VOLUME;
END;

// 3. R-Breaker价位计算 (螺纹钢优化)
// 动态R-Breaker参数
CalcBreakPoints:
// 时段调整系数
VAR: 
    SessionFactor = IIF(TIME < 0.1030, 1.2,    // 早盘活跃
                       IIF(TIME > 0.1430, 1.1, // 尾盘
                       1.0));                 // 其他时段

// 动态调整系数(基于波动率和成交量)
VolatilityRatio = ATR(ATRPeriod)/MA(CLOSE, 20);
VAR: VolumeRatio = VOLUME/MA(VOLUME, 20);

DynamicK = (K1 + K2 * (VolatilityRatio - 0.012)) 
         * (0.8 + 0.2*VolumeRatio)             // 成交量影响
         * SessionFactor;                      // 时段影响

// 关键价位(增加平滑处理)
ObserveSellPrice = EMA(DailyHigh + DynamicK * (PreClose - DailyLow), 3);
ObserveBuyPrice = EMA(DailyLow - DynamicK * (DailyHigh - PreClose), 3);

// 4. 趋势强度判断
TrendStrength:
VAR: 
    ADXValue(ADX(14)),
    MA5(EMA(CLOSE, 5)),
    MA20(EMA(CLOSE, 20));

//******************************************************************
//                 信号生成模块 (螺纹钢特化)
//******************************************************************

// 增强版信号生成
VAR: 
    TrendConfidence(0), // 趋势置信度(0-100)
    FlowStrength(0);    // 订单流强度

// 趋势置信度计算(多指标组合)
TrendConfidence = MIN(100, 
    30 * IIF(MA5 > MA20, 1, -1) 
  + 25 * IIF(ADXValue > 22, 1, 0) 
  + 20 * IIF(CLOSE > EMA(CLOSE, 50), 1, -1)
  + 15 * IIF(MACD().Hist > 0, 1, -1)
  + 10 * IIF(RSI(14) > 50, 1, -1));

// 订单流强度计算
FlowStrength = EMADelta * IIF(ABS(EMADelta) > 50, 1.2, 1.0)
             + 0.3 * (BuyMarketVol - SellMarketVol)/BigVol;

// 多头信号(多条件加权评分)
LongSignal = 
   (CLOSE >= ObserveSellPrice) * 25 +
   (ASKVOL1 > BigVol*0.7) * 20 +
   (FlowStrength < -45) * 25 +
   (TrendConfidence > 60) * 20 +
   ((BIDVOL1 + ASKVOL1) > MinLiquidity) * 10
   >= 70; // 总分阈值

// 空头信号
ShortSignal = 
   (CLOSE <= ObserveBuyPrice) * 25 +
   (BIDVOL1 > BigVol*0.7) * 20 +
   (FlowStrength > 45) * 25 +
   (TrendConfidence < -60) * 20 +
   ((BIDVOL1 + ASKVOL1) > MinLiquidity) * 10
   >= 70;

//******************************************************************
//                 仓位与风险管理
//******************************************************************

// 1. 动态仓位计算 (螺纹钢特化)
PositionManagement:
VAR:
    // 基础计算不变
    AccountEquity(TACCOUNT(1)),
    RiskUnit(MaxRiskRatio * AccountEquity),
    ContractValue(CLOSE * ContractMultiplier),
    BaseSize(INTPART(RiskUnit / (ATR(ATRPeriod) * ContractValue))),
    
    // 新增因子
    LiquidityScore(0),    // 流动性评分
    VolatilityScore(0),   // 波动率评分
    TrendScore(0),        // 趋势评分
    SlippageCost(0);      // 预估滑点成本

// 流动性评分(0-1)
LiquidityScore = MIN(1, (BIDVOL1 + ASKVOL1)/(2*MinLiquidity));

// 波动率评分(0-1.5)
VolatilityScore = 0.5 + VolatilityRatio/0.02;

// 趋势评分(基于趋势置信度)
TrendScore = 0.5 + ABS(TrendConfidence)/200;

// 预估滑点成本(单位:手)
SlippageCost = MAX(1, INTPART(VolatilityScore * 2));

// 综合仓位计算
PositionSize = MAX(1, 
    BaseSize * 
    (0.4 + 0.6 * LiquidityScore) *      // 流动性影响40-100%
    (0.7 + 0.3 * VolatilityScore) *     // 波动率影响70-115%
    (0.8 + 0.2 * TrendScore) -          // 趋势影响80-110%
    SlippageCost                         // 扣除滑点成本
);

DEBUG('【仓位】基础:',BaseSize,' 最终:',PositionSize,' 滑点:',SlippageCost);

// 2. 熔断机制
RiskControl:
// 单日最大亏损
IF TACCOUNT(3)/AccountEquity < DailyLossLimit THEN BEGIN
    TSELL(1, TBUYHOLDINGEX('', STKLABEL), MKT);
    TBUY(1, TSELLHOLDINGEX('', STKLABEL), MKT);
    TradingPaused = 1;
    DEBUG('【风控】单日最大损失触发');
END;

// 连续亏损控制
IF TISREMAIN(0) = -1 THEN BEGIN
    LossStreak = LossStreak + 1;
    IF LossStreak >= 3 THEN BEGIN
        PositionSize = MAX(1, PositionSize * 0.5);
        DEBUG('【风控】连续亏损降仓至:', PositionSize);
    END;
END ELSE
    LossStreak = 0;

// 盈利保护
IF TACCOUNT(3) > DailyMaxProfit THEN DailyMaxProfit = TACCOUNT(3);
IF TACCOUNT(3) < DailyMaxProfit * 0.7 THEN BEGIN
    DEBUG('【保护】回撤超过30%，部分平仓');
    IF TBUYHOLDINGEX('', STKLABEL) > 0 THEN
        TSELL(1, TBUYHOLDINGEX('', STKLABEL)*0.5, LMT, BID1);
    IF TSELLHOLDINGEX('', STKLABEL) > 0 THEN
        TBUY(1, TSELLHOLDINGEX('', STKLABEL)*0.5, LMT, ASK1);
END;

//******************************************************************
//                 交易执行模块
//******************************************************************

// 1. 信号执行
TradeExecution:
CurrentTime = TIMETOT0(SESSIONTIME);

// 多头开仓
IF LongSignal AND TradingPaused = 0 AND CurrentTime - LastTradeTime > 3000 THEN BEGIN
    // 清理未成交订单
    IF TISREMAIN(BKID) = 1 THEN TCANCEL(BKID);
    IF TISREMAIN(BPID) = 1 THEN TCANCEL(BPID);
    
    // 开多单
    BKID = TBUY(1, PositionSize, LIMIT, ASK1 + MINPRICE);
    DEBUG('【信号】多头开仓 价格:', ASK1, ' 手数:', PositionSize);
    
    // 平空头持仓
    IF TSELLHOLDINGEX('', STKLABEL) > 0 THEN
        BPID = TBUY(1, TSELLHOLDINGEX('', STKLABEL), LMT, ASK1);
    
    LastTradeTime = CurrentTime;
END

// 空头开仓
IF ShortSignal AND TradingPaused = 0 AND CurrentTime - LastTradeTime > 3000 THEN BEGIN
    IF TISREMAIN(SKID) = 1 THEN TCANCEL(SKID);
    IF TISREMAIN(SPID) = 1 THEN TCANCEL(SPID);
    
    SKID = TSELLSHORT(1, PositionSize, LIMIT, BID1 - MINPRICE);
    DEBUG('【信号】空头开仓 价格:', BID1, ' 手数:', PositionSize);
    
    IF TBUYHOLDINGEX('', STKLABEL) > 0 THEN
        SPID = TSELL(1, TBUYHOLDINGEX('', STKLABEL), LMT, BID1);
    
    LastTradeTime = CurrentTime;
END

// 2. 动态止盈止损 (螺纹钢特化)
// 增强版退出机制
DynamicExit:
VAR:
    LongStopPrice(0), ShortStopPrice(0),
    CurrentProfit(0), ProfitRatio(0),
    MaxHoldTime(180); // 最大持仓时间(秒)

// 多头持仓处理
IF TBUYHOLDINGEX('', STKLABEL) > 0 THEN BEGIN
    CurrentProfit = (CLOSE - TBUYAVGPRICE(STKLABEL)) * ContractMultiplier;
    ProfitRatio = CurrentProfit / (AccountEquity * MaxRiskRatio);
    
    // 动态退出机制 - 多条件组合
    VAR: ExitScore(0);
    ExitScore = 
        IIF(CLOSE < EMA(CLOSE, 5), 30, 0) +                  // 短期趋势反转
        IIF(FlowStrength > 20, 25, 0) +                      // 订单流反转
        IIF(CLOSE < Lowest(CLOSE, 3), 20, 0) +               // 价格新低
        IIF(CurrentTime - ENTRYTIME > MaxHoldTime, 15, 0) +  // 超时
        IIF(ProfitRatio < -1, 10, 0);                        // 亏损扩大
    
    // 根据评分确定退出价格
    IF ExitScore >= 50 THEN BEGIN
        VAR: ExitPrice = BID1 - MINPRICE * IIF(ExitScore > 70, 3, 1);
        TSELL(1, TBUYHOLDINGEX('', STKLABEL), LMT, ExitPrice);
        DEBUG('【退出】多头平仓 评分:', ExitScore, ' 价格:', ExitPrice);
    END;
END;

// 空头持仓处理
// 空头持仓处理(完整对称逻辑)
IF TSELLHOLDINGEX('', STKLABEL) > 0 THEN BEGIN
    CurrentProfit = (TSELLAVGPRICE(STKLABEL) - CLOSE) * ContractMultiplier;
    ProfitRatio = CurrentProfit / (AccountEquity * MaxRiskRatio);
    
    // 动态退出机制 - 多条件组合
    VAR: ExitScore(0);
    ExitScore = 
        IIF(CLOSE > EMA(CLOSE, 5), 30, 0) +                  // 短期趋势反转
        IIF(FlowStrength < -20, 25, 0) +                     // 订单流反转(空头方向)
        IIF(CLOSE > Highest(CLOSE, 3), 20, 0) +              // 价格新高
        IIF(CurrentTime - ENTRYTIME > MaxHoldTime, 15, 0) +  // 超时
        IIF(ProfitRatio < -1, 10, 0);                        // 亏损扩大
    
    // 根据评分确定退出价格
    IF ExitScore >= 50 THEN BEGIN
        VAR: ExitPrice = ASK1 + MINPRICE * IIF(ExitScore > 70, 3, 1);
        TBUY(1, TSELLHOLDINGEX('', STKLABEL), LMT, ExitPrice);
        DEBUG('【退出】空头平仓 评分:', ExitScore, ' 价格:', ExitPrice);
    END;
    
    // 三重保护性止损(与多头对称)
    VAR: 
        VolatilityStop = TSELLAVGPRICE(STKLABEL) * (1 + 0.01 * SQR(VolatilityRatio/0.012)),
        ProfitProtectStop = LOWEST(CLOSE, 5) * (1 + 0.007 * (CurrentProfit/AccountEquity)*100),
        TimeStop = IIF(CurrentTime - ENTRYTIME > 120 AND CLOSE > LOWEST(CLOSE, 5), 
                      ASK1 + MINPRICE*2, 
                      0);
    
    // 最终止损价取最激进的值
    ShortStopPrice = MIN(VolatilityStop, ProfitProtectStop);
    IF TimeStop > 0 THEN ShortStopPrice = MIN(ShortStopPrice, TimeStop);
    
    // 执行止损
    IF CLOSE >= ShortStopPrice THEN BEGIN
        TBUY(1, TSELLHOLDINGEX('', STKLABEL), LMT, ASK1);
        DEBUG('【止损】空头平仓 价格:', ASK1, 
              ' 波动止损价:', VolatilityStop,
              ' 盈利保护价:', ProfitProtectStop);
    END;
    
    // 趋势衰竭退出(ADX下降)
    IF ADX(14) < 20 AND DMI().DIPlus > DMI().DIMinus THEN BEGIN
        TBUY(1, TSELLHOLDINGEX('', STKLABEL)*0.5, LMT, ASK1); // 减半仓
        DEBUG('【趋势退出】空头减仓 ADX:', ADX(14), ' DMI+:', DMI().DIPlus);
    END;
END;

// 3. 收盘平仓
IF CurrentTime >= 145500 THEN BEGIN
    IF TBUYHOLDINGEX('', STKLABEL) > 0 THEN
        TSELL(1, TBUYHOLDINGEX('', STKLABEL), MKT);
    IF TSELLHOLDINGEX('', STKLABEL) > 0 THEN
        TBUY(1, TSELLHOLDINGEX('', STKLABEL), MKT);
END

//******************************************************************
//                 可视化与调试
//******************************************************************
// 增强版可视化
DRAWLINE1(ObserveSellPrice, COLORRED, '观察卖出价');
DRAWLINE2(ObserveBuyPrice, COLORBLUE, '观察买入价');
DRAWLINE3(EMA(CLOSE, 5), COLORCYAN, 'MA5');
DRAWLINE4(EMA(CLOSE, 20), COLORMAGENTA, 'MA20');

// 动态信息面板
DRAWTEXT('大单阈值:' + NUMTOSTR(BigVol,0) + 
         ' 动态系数:' + NUMTOSTR(DynamicRatio,2), COLORGRAY);
DRAWTEXT('订单流:' + NUMTOSTR(EMADelta,0) + 
         ' 强度:' + NUMTOSTR(FlowStrength,1),
         IIF(EMADelta>0, COLORRED, COLORGREEN));
DRAWTEXT('趋势置信度:' + NUMTOSTR(TrendConfidence,0) + '%', 
         IIF(TrendConfidence>0, COLORRED, COLORBLUE));

// 信号标记(更丰富的信息)
IF LongSignal THEN
    DRAWTEXT('▲L'+NUMTOSTR(PositionSize,0), LOW*0.998, COLORRED)
ELSE IF ShortSignal THEN
    DRAWTEXT('▼S'+NUMTOSTR(PositionSize,0), HIGH*1.002, COLORBLUE);

// 持仓状态(增加盈亏信息)
IF TBUYHOLDINGEX('', STKLABEL) > 0 THEN BEGIN
    VAR: Profit = (CLOSE - TBUYAVGPRICE(STKLABEL)) * ContractMultiplier;
    DRAWTEXT('持多:' + NUMTOSTR(TBUYHOLDINGEX('', STKLABEL),0) + '手' +
             ' 盈亏:' + NUMTOSTR(Profit,2), LOW*0.995, COLORRED);
END ELSE IF TSELLHOLDINGEX('', STKLABEL) > 0 THEN BEGIN
    VAR: Profit = (TSELLAVGPRICE(STKLABEL) - CLOSE) * ContractMultiplier;
    DRAWTEXT('持空:' + NUMTOSTR(TSELLHOLDINGEX('', STKLABEL),0) + '手' +
             ' 盈亏:' + NUMTOSTR(Profit,2), HIGH*1.005, COLORBLUE);
END;

